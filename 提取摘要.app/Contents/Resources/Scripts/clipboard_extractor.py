#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re
import subprocess
import sys
import os

def get_clipboard_content():
    """获取剪贴板内容"""
    try:
        result = subprocess.run(['pbpaste'], capture_output=True, text=True, encoding='utf-8')
        return result.stdout
    except Exception as e:
        print(f"Error reading clipboard: {e}")
        return ""

def set_clipboard_content(content):
    """设置剪贴板内容"""
    try:
        process = subprocess.Popen(['pbcopy'], stdin=subprocess.PIPE, text=True, encoding='utf-8')
        process.communicate(input=content)
    except Exception as e:
        print(f"Error setting clipboard: {e}")

def show_notification(title, message):
    """显示系统通知"""
    try:
        # 显示简短的对话框（1.5秒后自动消失）
        dialog_script = f'display dialog "{message}" with title "{title}" buttons {{"确定"}} default button 1 giving up after 1.5'
        subprocess.run(['osascript', '-e', dialog_script])

    except Exception:
        # 如果对话框失败，播放提示音
        try:
            subprocess.run(['afplay', '/System/Library/Sounds/Glass.aiff'], capture_output=True)
        except:
            pass

def show_extraction_mode_dialog():
    """显示提取模式选择对话框"""
    try:
        dialog_script = '''
        display dialog "请选择提取模式：" with title "提取摘要" buttons {"反引号(`)", "双等号(==)"} default button 1
        '''
        result = subprocess.run(['osascript', '-e', dialog_script],
                              capture_output=True, text=True, encoding='utf-8')

        if result.returncode == 0:
            # 检查用户选择的按钮
            if "反引号" in result.stdout:
                return "backtick"  # 反引号模式
            else:
                return "double_equals"  # 双等号模式
        else:
            # 用户取消或出错，默认使用反引号模式
            return "backtick"

    except Exception:
        # 出错时默认使用反引号模式
        return "backtick"

def show_output_choice_dialog():
    """显示输出选择对话框"""
    try:
        dialog_script = '''
        display dialog "请选择输出模式：" with title "提取摘要" buttons {"仅摘要", "摘要+原文"} default button 2
        '''
        result = subprocess.run(['osascript', '-e', dialog_script],
                              capture_output=True, text=True, encoding='utf-8')

        if result.returncode == 0:
            # 检查用户选择的按钮
            if "仅摘要" in result.stdout:
                return True  # 仅摘要模式
            else:
                return False  # 摘要+原文模式
        else:
            # 用户取消或出错，默认使用摘要+原文模式
            return False

    except Exception:
        # 出错时默认使用摘要+原文模式
        return False

def extract_backtick_content(text):
    """提取``包围的内容，但不包括```代码块"""
    # 先移除```代码块内容，避免干扰
    text_without_code_blocks = re.sub(r'```[\s\S]*?```', '', text)

    # 提取单个``包围的内容
    pattern = r'`([^`\n]+)`'
    matches = re.findall(pattern, text_without_code_blocks)

    # 去重并保持顺序
    unique_matches = []
    seen = set()
    for match in matches:
        if match not in seen:
            unique_matches.append(match)
            seen.add(match)

    return unique_matches

def extract_double_equals_content(text):
    """提取==包围的内容"""
    # 提取==包围的内容，支持跨行
    pattern = r'==([^=]+?)=='
    matches = re.findall(pattern, text, re.DOTALL)

    # 清理匹配结果：去除首尾空白字符
    cleaned_matches = []
    seen = set()
    for match in matches:
        cleaned_match = match.strip()
        if cleaned_match and cleaned_match not in seen:
            cleaned_matches.append(cleaned_match)
            seen.add(cleaned_match)

    return cleaned_matches

def main():
    # 读取剪贴板内容
    original_text = get_clipboard_content()

    if not original_text.strip():
        return

    # 显示提取模式选择对话框
    extraction_mode = show_extraction_mode_dialog()

    # 根据选择的模式提取内容
    if extraction_mode == "double_equals":
        extracted_items = extract_double_equals_content(original_text)
        mode_name = "双等号(==)"
    else:
        extracted_items = extract_backtick_content(original_text)
        mode_name = "反引号(`)"

    if not extracted_items:
        show_notification("提取摘要", f"❌ 未找到任何{mode_name}标记的内容")
        return

    # 显示输出选择对话框
    summary_only = show_output_choice_dialog()

    # 格式化摘要 - 每条之间换行
    summary_lines = []
    for item in extracted_items:
        summary_lines.append(item)

    summary_content = '\n'.join(summary_lines)

    # 根据用户选择决定输出内容
    if summary_only:
        # 仅输出摘要内容
        output = f"摘要：\n{summary_content}"
        notification_msg = f"✅ 处理完成！已提取 {len(extracted_items)} 个摘要项目（仅摘要）"
    else:
        # 输出摘要和原文
        output = f"摘要：\n{summary_content}\n\n原文：\n{original_text}"
        notification_msg = f"✅ 处理完成！已提取 {len(extracted_items)} 个摘要项目"

    # 将结果复制到剪贴板
    set_clipboard_content(output)

    # 显示完成提醒
    show_notification("提取摘要", notification_msg)

if __name__ == "__main__":
    main()