#!/bin/bash

# 清理文档脚本
# 功能：移除标题、星号开头的行、多余空行，并随机打乱每行

input_file="文档/新文档.md"
output_file="文档/新文档_cleaned.md"

# 检查输入文件是否存在
if [ ! -f "$input_file" ]; then
    echo "错误：文件 '$input_file' 不存在"
    exit 1
fi

echo "正在清理文档..."

# 处理文档：
# 1. 移除以 ### 开头的标题行
# 2. 移除星号开头行的星号和空格，保留内容
# 3. 移除空行
# 4. 随机打乱行的顺序
grep -v '^###' "$input_file" | \
sed 's/^\*[[:space:]]*//' | \
grep -v '^[[:space:]]*$' | \
sort -R > "$output_file"

# 统计处理结果
original_lines=$(wc -l < "$input_file")
cleaned_lines=$(wc -l < "$output_file")

echo "清理完成！"
echo "原始文件行数: $original_lines"
echo "清理后行数: $cleaned_lines"
echo "输出文件: $output_file"

# 显示前10行预览
echo ""
echo "清理后的前10行预览："
echo "===================="
head -10 "$output_file"
