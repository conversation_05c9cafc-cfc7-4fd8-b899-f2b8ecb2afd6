import pysubs2
import sys
import os

# --- 样式定义 ---
# 您可以在这里修改或添加新的样式
# FontName: 字体名称，请确保系统中已安装
# FontSize: 字号
# PrimaryColour: 主要文字颜色
# BackColour: 背景框颜色
# BorderStyle: 3=背景框, 1=描边+阴影
# Alignment: 2=底部居中
# MarginV: 垂直边距
styles = {
    "Style_Classic": pysubs2.SSAStyle(
        fontname="PingFang SC",
        fontsize=24,
        primarycolor=pysubs2.Color(255, 255, 255), # 白色
        backcolor=pysubs2.Color(0, 0, 0, 128),     # 50% 透明黑
        borderstyle=3, # 背景框
        alignment=pysubs2.Alignment.BOTTOM_CENTER,
        marginv=30,
        outline=1,
        shadow=0.5
    ),
    "Style_Modern": pysubs2.SSAStyle(
        fontname="PingFang SC",
        fontsize=26,
        primarycolor=pysubs2.Color(255, 255, 255), # 白色
        outlinecolor=pysubs2.Color(17, 17, 17),   # 深灰色描边
        borderstyle=1, # 描边 + 阴影
        alignment=pysubs2.Alignment.BOTTOM_CENTER,
        marginv=30,
        outline=1.5,
        shadow=0.8
    ),
    "Style_Elegant": pysubs2.SSAStyle(
        fontname="Heiti SC", # 可以尝试不同字体，如黑体-简
        fontsize=24,
        primarycolor=pysubs2.Color(255, 238, 221), # 柔和的淡黄色
        outlinecolor=pysubs2.Color(0, 0, 0),       # 黑色描边
        borderstyle=1,
        alignment=pysubs2.Alignment.BOTTOM_CENTER,
        marginv=30,
        outline=1.2,
        shadow=0.5
    )
}

def convert_srt_to_styled_ass(srt_path):
    """
    将单个 SRT 文件转换为多个带有不同样式的 ASS 文件。
    """
    if not os.path.exists(srt_path):
        print(f"错误：文件不存在 {srt_path}")
        return

    try:
        # 加载 SRT 文件，自动尝试 UTF-8 和 GBK 编码
        try:
            subs = pysubs2.load(srt_path, encoding="utf-8")
        except UnicodeDecodeError:
            subs = pysubs2.load(srt_path, encoding="gbk") # 如果 UTF-8 失败，则尝试 GBK
        
        # 获取不带扩展名的基本文件名和所在目录
        base_name = os.path.splitext(os.path.basename(srt_path))[0]
        output_dir = os.path.dirname(srt_path)

        for style_name, style_obj in styles.items():
            # 为每种样式创建一个新的 SSAFile 对象
            styled_subs = pysubs2.SSAFile()
            # 将样式赋给 "Default" 样式名，所有字幕事件默认使用它
            styled_subs.styles["Default"] = style_obj
            styled_subs.events = subs.events

            # 生成输出文件名
            output_ass_path = os.path.join(output_dir, f"{base_name}_{style_name}.ass")
            
            # 保存为 ASS 文件
            styled_subs.save(output_ass_path)
            print(f"成功创建样式化字幕: {output_ass_path}")

    except Exception as e:
        print(f"处理文件 {srt_path} 时出错: {e}")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("使用方法: python srt_to_ass_converter.py <你的_srt_文件1.srt> [<你的_srt_文件2.srt>...]")
        # 作为示例，处理用户工作区中的一个文件
        print("\n未提供文件路径，将使用示例文件进行处理...")
        example_file = "哈佛公开课积极心理学/中英字幕/积极心理学_01什么是积极心理学.srt"
        convert_srt_to_styled_ass(example_file)
    else:
        # 处理命令行中提供的所有文件
        for srt_file_path in sys.argv[1:]:
            convert_srt_to_styled_ass(srt_file_path) 