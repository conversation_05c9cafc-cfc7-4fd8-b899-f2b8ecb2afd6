#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正确的顺序标记脚本：分析每个关键词在原文中的出现次数，记录被标记的是第几次出现，
然后在字幕中标记相应位次的关键词
"""

import re
import os
import sys
import argparse
from typing import List, Tuple, Dict

class CorrectSequentialMarker:
    def __init__(self):
        self.word_occurrence_marks = {}
        
    def analyze_word_occurrences(self, original_text: str) -> Dict[str, List[int]]:
        """分析每个关键词在原文中的出现次数，记录被标记的是第几次出现"""
        
        # 第一步：移除所有标记，获得纯净文本
        clean_text = re.sub(r'`([^`]+)`', r'\1', original_text)
        
        # 第二步：找到所有被标记的词语
        marked_pattern = r'`([^`]+)`'
        marked_matches = re.finditer(marked_pattern, original_text)
        
        word_occurrence_marks = {}
        
        for match in marked_matches:
            marked_word = match.group(1)
            
            # 计算这个标记在原文中的位置（去除标记符号后的位置）
            marked_pos_in_original = match.start()
            # 计算在这个位置之前有多少个标记符号
            text_before_mark = original_text[:marked_pos_in_original]
            marks_before = len(re.findall(r'`[^`]+`', text_before_mark))
            # 调整位置（每个标记有2个反引号）
            actual_pos = marked_pos_in_original - marks_before * 2
            
            # 在纯净文本中找到这个词语是第几次出现
            occurrence_number = self.find_occurrence_number(clean_text, marked_word, actual_pos)
            
            if marked_word not in word_occurrence_marks:
                word_occurrence_marks[marked_word] = []
            word_occurrence_marks[marked_word].append(occurrence_number)
        
        return word_occurrence_marks
    
    def find_occurrence_number(self, clean_text: str, word: str, target_pos: int) -> int:
        """找到指定位置的词语是该词语的第几次出现"""
        occurrence_count = 0
        start = 0
        
        while True:
            pos = clean_text.lower().find(word.lower(), start)
            if pos == -1:
                break
            
            occurrence_count += 1
            
            # 如果这个位置就是我们要找的位置（允许一定误差）
            if abs(pos - target_pos) <= len(word):
                return occurrence_count
            
            start = pos + 1
        
        return occurrence_count  # 如果没找到精确匹配，返回最后的计数
    
    def parse_srt_file(self, srt_path: str) -> List[Dict]:
        """解析SRT字幕文件"""
        try:
            with open(srt_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except UnicodeDecodeError:
            try:
                with open(srt_path, 'r', encoding='gbk') as f:
                    content = f.read()
            except UnicodeDecodeError:
                with open(srt_path, 'r', encoding='latin-1') as f:
                    content = f.read()
        
        blocks = re.split(r'\n\s*\n', content.strip())
        subtitles = []
        
        for block in blocks:
            lines = block.strip().split('\n')
            if len(lines) >= 3:
                subtitle_id = lines[0]
                timestamp = lines[1]
                text = ' '.join(lines[2:])
                
                subtitles.append({
                    'id': subtitle_id,
                    'timestamp': timestamp,
                    'text': text,
                    'original_text': text
                })
        
        return subtitles
    
    def find_word_all_positions(self, text: str, word: str) -> List[Tuple[int, int]]:
        """找到某个词语在文本中的所有位置"""
        positions = []
        start = 0
        
        while True:
            pos = text.lower().find(word.lower(), start)
            if pos == -1:
                break
            positions.append((pos, pos + len(word)))
            start = pos + 1
        
        return positions
    
    def mark_subtitles_by_occurrence(self, subtitles: List[Dict], word_occurrence_marks: Dict[str, List[int]]) -> List[Dict]:
        """根据词语出现次数标记字幕"""
        
        # 合并所有字幕文本，用于全局位置计算
        all_subtitle_text = ' '.join([sub['text'] for sub in subtitles])
        
        # 记录需要标记的全局位置
        global_marks = []
        
        for word, occurrence_numbers in word_occurrence_marks.items():
            # 找到这个词语在所有字幕中的位置
            word_positions = self.find_word_all_positions(all_subtitle_text, word)
            
            print(f"词语 '{word}' 在字幕中共找到 {len(word_positions)} 次出现")
            print(f"需要标记的出现次数: {occurrence_numbers}")
            
            # 标记指定次数的出现
            for occurrence_num in occurrence_numbers:
                if occurrence_num <= len(word_positions):
                    # 数组索引从0开始，所以第N次出现的索引是N-1
                    pos_start, pos_end = word_positions[occurrence_num - 1]
                    global_marks.append((pos_start, pos_end, word, occurrence_num))
                    print(f"  标记第 {occurrence_num} 次出现: 位置 {pos_start}-{pos_end}")
                else:
                    print(f"  ⚠️ 警告：需要标记第 {occurrence_num} 次出现，但字幕中只有 {len(word_positions)} 次出现")
        
        # 按位置排序，避免重叠
        global_marks.sort()
        
        # 将全局位置转换为各个字幕的局部位置并应用标记
        current_pos = 0
        for subtitle in subtitles:
            text = subtitle['text']
            text_start = current_pos
            text_end = current_pos + len(text)
            
            # 找到在当前字幕范围内的标记
            local_marks = []
            for mark_start, mark_end, word, occurrence_num in global_marks:
                if text_start <= mark_start < text_end:
                    local_start = mark_start - text_start
                    local_end = mark_end - text_start
                    local_marks.append((local_start, local_end, word, occurrence_num))
            
            # 从后往前应用标记，避免位置偏移
            local_marks.sort(reverse=True)
            new_text = text
            for local_start, local_end, word, occurrence_num in local_marks:
                new_text = new_text[:local_start] + '`' + new_text[local_start:local_end] + '`' + new_text[local_end:]
            
            subtitle['text'] = new_text
            current_pos = text_end + 1  # +1 for space between subtitles
        
        return subtitles
    
    def save_marked_srt(self, subtitles: List[Dict], output_path: str):
        """保存标记后的SRT文件"""
        with open(output_path, 'w', encoding='utf-8') as f:
            for subtitle in subtitles:
                f.write(f"{subtitle['id']}\n")
                f.write(f"{subtitle['timestamp']}\n")
                f.write(f"{subtitle['text']}\n\n")
    
    def read_original_text(self, text_path: str) -> str:
        """读取原始文本文件"""
        try:
            with open(text_path, 'r', encoding='utf-8') as f:
                return f.read()
        except UnicodeDecodeError:
            try:
                with open(text_path, 'r', encoding='gbk') as f:
                    return f.read()
            except UnicodeDecodeError:
                with open(text_path, 'r', encoding='latin-1') as f:
                    return f.read()
    
    def process(self, original_text: str, srt_file_path: str, output_path: str = None):
        """主处理函数"""
        print("正在分析原文中每个关键词的出现次数和标记位次...")
        
        word_occurrence_marks = self.analyze_word_occurrences(original_text)
        
        if not word_occurrence_marks:
            print("❌ 错误：在原文中没有找到任何被``标记的词语！")
            return None
        
        print(f"\n找到 {len(word_occurrence_marks)} 个被标记的关键词:")
        total_marks = 0
        for word, occurrences in word_occurrence_marks.items():
            print(f"'{word}': 需要标记第 {occurrences} 次出现")
            total_marks += len(occurrences)
        
        print(f"总共需要标记 {total_marks} 个位置")
        
        print(f"\n正在解析SRT字幕文件: {srt_file_path}")
        try:
            subtitles = self.parse_srt_file(srt_file_path)
            print(f"解析了 {len(subtitles)} 条字幕")
        except FileNotFoundError:
            print(f"❌ 错误：找不到字幕文件 {srt_file_path}")
            return None
        except Exception as e:
            print(f"❌ 错误：解析字幕文件时出错 - {e}")
            return None
        
        print("\n正在按出现次数标记字幕...")
        marked_subtitles = self.mark_subtitles_by_occurrence(subtitles, word_occurrence_marks)
        
        if output_path is None:
            base_name = os.path.splitext(srt_file_path)[0]
            output_path = f"{base_name}_正确顺序标记版.srt"
        
        print(f"\n正在保存标记后的字幕文件到: {output_path}")
        self.save_marked_srt(marked_subtitles, output_path)
        
        print("处理完成！")
        return output_path

def main():
    parser = argparse.ArgumentParser(
        description='正确的顺序标记脚本：分析每个关键词在原文中的出现次数，记录被标记的是第几次出现，然后在字幕中标记相应位次的关键词',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python3 正确的顺序标记脚本.py -t 原始文本.txt -s 字幕.srt
  python3 正确的顺序标记脚本.py -t 原始文本.txt -s 字幕.srt -o 输出字幕.srt

算法说明:
  1. 分析每个关键词在原文中的总出现次数
  2. 记录被``标记的是该关键词的第几次出现
  3. 在字幕中找到相同关键词的所有出现位置
  4. 只标记对应出现次数的位置

例如：原文中"下沉"出现12次，第3、5、7次被标记，
则在字幕中只标记"下沉"的第3、5、7次出现。
        """
    )
    
    parser.add_argument('-t', '--text', required=True, 
                       help='原始文本文件路径（包含用``标记的关键词）')
    parser.add_argument('-s', '--srt', required=True,
                       help='SRT字幕文件路径')
    parser.add_argument('-o', '--output', 
                       help='输出文件路径（可选，默认自动生成）')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.text):
        print(f"❌ 错误：找不到原始文本文件 {args.text}")
        sys.exit(1)
    
    if not os.path.exists(args.srt):
        print(f"❌ 错误：找不到字幕文件 {args.srt}")
        sys.exit(1)
    
    marker = CorrectSequentialMarker()
    
    try:
        print(f"正在读取原始文本文件: {args.text}")
        original_text = marker.read_original_text(args.text)
        
        output_file = marker.process(original_text, args.srt, args.output)
        
        if output_file:
            print(f"\n✅ 成功生成正确顺序标记版字幕文件: {output_file}")
        else:
            print(f"\n❌ 处理失败")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ 处理过程中出现错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
