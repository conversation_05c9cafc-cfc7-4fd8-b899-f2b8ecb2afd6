#!/bin/bash

# SRT音效生成器 - 主运行脚本
# 作为AppleScript和Python脚本之间的桥梁

# 设置脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PYTHON_SCRIPT="$SCRIPT_DIR/srt_processor.py"

# 设置环境变量
export PATH="/opt/homebrew/bin:/usr/local/bin:$PATH"

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >&2
}

# 错误处理函数
error_exit() {
    log "错误: $1"
    exit 1
}

# 检查参数
if [ $# -lt 3 ]; then
    error_exit "参数不足。用法: $0 <srt_file> <audio_file> <output_file>"
fi

SRT_FILE="$1"
AUDIO_FILE="$2"
OUTPUT_FILE="$3"

log "开始处理SRT音效生成..."
log "SRT文件: $SRT_FILE"
log "音效文件: $AUDIO_FILE"
log "输出文件: $OUTPUT_FILE"

# 检查必要文件
if [ ! -f "$SRT_FILE" ]; then
    error_exit "SRT文件不存在: $SRT_FILE"
fi

if [ ! -f "$AUDIO_FILE" ]; then
    error_exit "音效文件不存在: $AUDIO_FILE"
fi

if [ ! -f "$PYTHON_SCRIPT" ]; then
    error_exit "Python脚本不存在: $PYTHON_SCRIPT"
fi

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    error_exit "Python3 未安装或不在PATH中"
fi

# 检查FFmpeg
if ! command -v ffmpeg &> /dev/null; then
    error_exit "FFmpeg 未安装或不在PATH中。请运行: brew install ffmpeg"
fi

log "环境检查完成"

# 调用Python脚本
log "调用Python处理脚本..."
python3 "$PYTHON_SCRIPT" "$SRT_FILE" --audio "$AUDIO_FILE" "$OUTPUT_FILE"

# 检查Python脚本执行结果
PYTHON_EXIT_CODE=$?
if [ $PYTHON_EXIT_CODE -eq 0 ]; then
    log "Python脚本执行成功"
    
    # 检查输出文件是否生成
    if [ -f "$OUTPUT_FILE" ]; then
        log "音效轨道生成成功: $OUTPUT_FILE"
        exit 0
    else
        error_exit "输出文件未生成"
    fi
else
    error_exit "Python脚本执行失败，退出码: $PYTHON_EXIT_CODE"
fi
