<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/Scripts/main.scpt</key>
		<data>
		tkFIGbOxJpWEga8uJwxqzxoqWiE=
		</data>
		<key>Resources/droplet.icns</key>
		<data>
		qNNSP6gATivEfI1mX4+VCxUouHQ=
		</data>
		<key>Resources/droplet.rsrc</key>
		<data>
		ar5xmDs9mPpaINVXiVlVqUCB8ao=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Resources/Scripts/main.scpt</key>
		<dict>
			<key>hash</key>
			<data>
			tkFIGbOxJpWEga8uJwxqzxoqWiE=
			</data>
			<key>hash2</key>
			<data>
			8zP9+OusYtZhKe5JAG2RTArdVE+ssB56YQsD6Qh+Ie4=
			</data>
		</dict>
		<key>Resources/droplet.icns</key>
		<dict>
			<key>hash</key>
			<data>
			qNNSP6gATivEfI1mX4+VCxUouHQ=
			</data>
			<key>hash2</key>
			<data>
			8bc0JqW+sc2ju5zB4gkHAD9/lxjPcq52eiav7nh8dZ4=
			</data>
		</dict>
		<key>Resources/droplet.rsrc</key>
		<dict>
			<key>hash</key>
			<data>
			ar5xmDs9mPpaINVXiVlVqUCB8ao=
			</data>
			<key>hash2</key>
			<data>
			NV2dNLwgykV7aWfTBywUILBA9Lidl7F51PrkQw77SbM=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
