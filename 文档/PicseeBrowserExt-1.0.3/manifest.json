{"manifest_version": 2, "name": "Picsee Extension", "description": "This extension is for <PERSON><PERSON><PERSON> to collection image、video.", "version": "1.0.3", "permissions": ["<all_urls>", "activeTab", "contextMenus", "storage", "http://*/*", "https://*/*", "tabs"], "icons": {"16": "public/assets/icon16.png", "48": "public/assets/icon48.png", "128": "public/assets/icon128.png"}, "browser_action": {"default_icon": "public/assets/icon19.png", "default_popup": "popup/index.html"}, "background": {"page": "background/index.html"}, "options_ui": {"page": "options/index.html", "chrome_style": true, "open_in_tab": true}, "commands": {}, "content_security_policy": "script-src 'self' 'unsafe-eval'; object-src 'self'", "content_scripts": [{"match_about_blank": true, "js": ["contentScripts/index.js"], "css": ["contentScripts/index.css"], "matches": ["http://*/*", "https://*/*"], "all_frames": false, "run_at": "document_end"}], "web_accessible_resources": ["contentScripts/*", "public/assets/*", "popup/*", "options/*"]}