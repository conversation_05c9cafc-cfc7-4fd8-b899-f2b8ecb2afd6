{"version": 3, "sources": ["index.css"], "names": [], "mappings": "AAEA,KACI,WAAY,CACZ,YAAa,CACb,QAAS,CACT,eAAgB,CAChB,4KAAsM,CACtM,eACJ,CAEA,MACI,YACJ,CAGA,eACI,iBAAkB,CAClB,WAAY,CACZ,aACJ,CAEA,0BACI,iBAAkB,CAClB,WAAY,CACZ,gBAAiB,CACjB,qBAAsB,CACtB,qBAAsB,CACtB,wBAAyB,CACzB,oBAAqB,CACrB,eACJ,CAEA,gCACI,kBACJ,CAEA,6CACI,YACJ,CAEA,6CACI,aACJ,CAMA,gFACI,UACJ,CAEA,wCACI,aACJ,CAEA,yCACI,aACJ,CAEA,kBACI,QAAS,CACT,UAAW,CACX,YAAa,CACb,wBACJ,CAEA,gCACI,iBAAkB,CAClB,kBAAmB,CACnB,oBAAqB,CACrB,UAAW,CACX,WAAY,CACZ,gBAAiB,CACjB,iBACJ,CAEA,uCACI,aACJ,CAEA,uCACI,YACJ,CAEA,oCACI,iBAAkB,CAClB,KAAM,CACN,UAAW,CACX,cAAe,CACf,aAAc,CACd,UAAY,CACZ,gBAAiB,CACjB,qDACJ,CAEA,oCACI,qBAAsB,CACtB,oBAAqB,CACrB,cAAe,CACf,eAAgB,CAChB,YACJ,CAEA,gCACI,kBAAmB,CACnB,oBAAqB,CACrB,cAAe,CACf,eAAgB,CAChB,iBAAkB,CAClB,aACJ", "file": "popup.622a2d5e.css", "sourceRoot": "../../popup", "sourcesContent": ["\n/* help for body */\nbody {\n    width: 212px;\n    height: 104px;\n    margin: 0;\n    background: #fff;\n    font-family: -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", Helvetica, Arial, \"PingFang SC\",  \"Hiragino Sans GB\", \"Microsoft Yahei\", \"Heiti SC\",\"WenQuanYi Micro Hei\", \"PingFang TC\", sans-serif;\n    overflow: hidden;\n}\n\n.hide {\n    display: none;\n}\n\n/* help for dropdown menu */\n.dropdown-menu {\n    position: absolute;\n    width: 212px;\n    padding: 4px 0px;\n}\n\n.dropdown-menu .menu-item {\n    position: relative;\n    height: 28px;\n    line-height: 28px;\n    background-color: #fff;\n    -moz-user-select: none;\n    -webkit-user-select: none;\n    -ms-user-select: none;\n    border-radius: 0;\n}\n\n.dropdown-menu .menu-item:hover {\n    background: #F1F2F4;\n}\n\n.dropdown-menu .menu-item:hover .icon.normal {\n    display: none;\n}\n\n.dropdown-menu .menu-item:hover .icon.active {\n    display: block;\n}\n\n.dropdown-menu .menu-item:hover .name {\n    color: #111;\n}\n\n.dropdown-menu .menu-item:hover .shortcut {\n    color: #111;\n}\n\n.dropdown-menu .menu-item .state-enable {\n    color: #1372FB;\n}\n\n.dropdown-menu .menu-item .state-disable {\n    color: #FF0016;\n}\n\n.dropdown-menu hr {\n    border: 0;\n    height: 1px;\n    margin: 5px 0;\n    background-color: #e9e9e9;\n}\n\n.dropdown-menu .menu-item .icon {\n    position: absolute;\n    vertical-align: top;\n    display: inline-block;\n    width: 44px;\n    height: 28px;\n    line-height: 28px;\n    text-align: center;\n}\n\n.dropdown-menu .menu-item .icon.normal {\n    display: block;\n}\n\n.dropdown-menu .menu-item .icon.active {\n    display: none;\n}\n\n.dropdown-menu .menu-item .shortcut {\n    position: absolute;\n    top: 0;\n    right: 16px;\n    font-size: 12px;\n    color: #43474e;\n    opacity: 0.7;\n    line-height: 28px;\n    font-family: Arial,\"Helvetica Neue\",Helvetica,sans-serif;\n}\n\n.dropdown-menu .menu-item .icon img {\n    vertical-align: middle;\n    display: inline-block;\n    max-width: 14px;\n    max-height: 14px;\n    display: none;\n}\n\n.dropdown-menu .menu-item .name {\n    vertical-align: top;\n    display: inline-block;\n    font-size: 13px;\n    font-weight: 400;\n    padding-left: 16px;\n    color: #43474e;\n}"]}