

/* drop area */
.picsee-drop-area {
    user-select: none;
    font-family: "PingFang SC", "Hiragino Sans GB", "WenQuanYi Micro Hei", "Microsoft Yahei", "微软雅黑", "Helvetica Neue", Helvetica, Arial, Arial, sans-serif;
    font-weight: 400;
    z-index: 2147483647;
    position: fixed;
    top: -999999px;
    left: -999999px;
    width: 200px;
    text-align: center;
    transition: background 0.1s ease-in-out, opacity 0.1s ease-in-out, transform 0.2s ease-in-out, border 0.1s ease-in-out, box-shadow 0.1s ease-in-out;
    opacity: 0;
    padding: 0;
    transform: translateY(10px);
    border-radius: 10px;
    box-sizing: border-box;
    opacity: 0.8;
    background: #fff;
    box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.20);
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid rgba(0, 0, 0, 0.5);
}


.picsee-drop-area.show {
    transition: transform 50ms ease;
    transform: translateY(0px);
}

.picsee-drop-area.dragover {
    opacity: 1;
}

#picsee-drop-area-overlay {
    z-index: 1147483646;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    background: rgba(33, 33, 33, 0.5);
    user-select: none;
    pointer-events: none;
    transition: opacity 50ms ease;
}

.picsee-drop-area .picsee-drop-area-content {
    display:flex;
    justify-content:center;
    align-items: center;
    width: 200px;
    min-height: 200px;
}


.picsee-drop-area .picsee-drop-area-content .icon {
    height: 32px;
    width: 32px;
    line-height: 32px;
    text-align: center;
    margin: 0 auto 20px !important;
    background: rgba(128, 128, 128, 0.20);
    border-radius: 50%;
    transition: all 0.3s ease-in-out;
}

.picsee-drop-area .picsee-drop-area-content .icon .fake-svg {
    -webkit-mask: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz48c3ZnIHdpZHRoPSIxM3B4IiBoZWlnaHQ9IjE1cHgiIHZpZXdCb3g9IjAgMCAxMyAxNSIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj4gICAgICAgIDx0aXRsZT5pY19kb3dubG9hZF9ub3JtYWw8L3RpdGxlPiAgICA8ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz4gICAgPGcgaWQ9IlBhZ2UtMSIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+ICAgICAgICA8ZyBpZD0i5paw54mI5pys5ouW5ou95qaC5b+1LS0t5ouW5ou96Kem5Y+RIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgtODE0LjAwMDAwMCwgLTM2Mi4wMDAwMDApIiBmaWxsPSIjMzMzMzMzIj4gICAgICAgICAgICA8ZyBpZD0iR3JvdXAtMyIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoNzA5LjAwMDAwMCwgMjg5LjAwMDAwMCkiPiAgICAgICAgICAgICAgICA8ZyBpZD0iR3JvdXAtNyIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMjUuMDAwMDAwLCA2MS4wMDAwMDApIj4gICAgICAgICAgICAgICAgICAgIDxnIGlkPSJHcm91cC02IiB0cmFuc2Zvcm09InRyYW5zbGF0ZSg2Ny40NTAwMDAsIDAuMDAwMDAwKSI+ICAgICAgICAgICAgICAgICAgICAgICAgPGcgaWQ9ImFycm93LWRvd24iIHRyYW5zZm9ybT0idHJhbnNsYXRlKDEyLjM1MDAwMCwgMTIuMzUwMDAwKSI+ICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwb2x5Z29uIGlkPSJpY19kb3dubG9hZF9ub3JtYWwiIHBvaW50cz0iNi42NSAxMy43NzUgMC40NzUgNy42IDMuOCA3LjYgMy44IDAgOS41IDAgOS41IDcuNiAxMi44MjUgNy42Ij48L3BvbHlnb24+ICAgICAgICAgICAgICAgICAgICAgICAgPC9nPiAgICAgICAgICAgICAgICAgICAgPC9nPiAgICAgICAgICAgICAgICA8L2c+ICAgICAgICAgICAgPC9nPiAgICAgICAgPC9nPiAgICA8L2c+PC9zdmc+) no-repeat center center;
    height: 32px;
    width: 32px;
    background: #666;
    transition: all 0.3s ease-in-out;
    animation: icon-jump 1.5s ease 0s infinite normal;
}


.picsee-drop-area .picsee-drop-area-content .title {
    font-family: "PingFang SC", "Hiragino Sans GB", "WenQuanYi Micro Hei", "Microsoft Yahei", "微软雅黑", "Helvetica Neue", Helvetica, Arial, Arial, sans-serif;
    font-size: 16px !important;
    font-weight: 400 !important;
    line-height: 1 !important;
    overflow: hidden !important;
    white-space: nowrap !important;
    text-overflow: ellipsis !important;
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
    color: #111 !important;
    margin: 0 !important;
    display: block !important;
    transition: all 0.3s ease-in-out;
}

.picsee-drop-area .picsee-drop-area-content .description {
    font-family: "PingFang SC", "Hiragino Sans GB", "WenQuanYi Micro Hei", "Microsoft Yahei", "微软雅黑", "Helvetica Neue", Helvetica, Arial, Arial, sans-serif;
    font-size: 14px !important;
    font-weight: 400 !important;
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
    margin-top: 12px !important;
    margin-bottom: 0 !important;
    line-height: 1.5 !important;
    color: #111 !important;
    opacity: 0.6;
    transition: all 0.3s ease-in-out;
}


/* drop area overlay */
#picsee-drop-area-overlay {
    z-index: 1147483646;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    background: rgba(33, 33, 33, 0.5);
    user-select: none;
    pointer-events: none;
    transition: opacity 50ms ease;
}

#picsee-drop-area-overlay.show {
    opacity: 1;
}

